# BayarCash E-Mandate Integration Setup

This guide will help you set up and test the new BayarCash E-Mandate/FPX Direct Debit integration.

## Quick Setup

### 1. Run Database Migration

```bash
php artisan migrate
```

This will create the `bayarcash_emandate_enrollments` table.

### 2. Verify Configuration

Ensure your `.env` file has the BayarCash configuration:

```env
BC_API_TOKEN=your_api_token
BC_API_SECRET_KEY=your_secret_key
BC_PORTAL_KEY=your_portal_key
BC_SANDBOX=true
BC_API_VERSION=v3
```

### 3. Test the Integration

Visit the test page to verify everything is working:

```
http://your-domain/test/bayarcash-emandate
```

## Available Routes

### Web Interface
- `/payment/bayarcash-emandate/enrollment` - E-mandate enrollment form
- `/payment/bayarcash-emandate/status-checker` - Check enrollment status
- `/test/bayarcash-emandate` - Testing interface

### API Endpoints
- `POST /api/bayarcash-emandate/create` - Create enrollment
- `POST /api/bayarcash-emandate/check-status` - Check status
- `GET /api/bayarcash-emandate/my-enrollments` - User enrollments

## Testing Flow

1. **Create Enrollment**: Use the enrollment form with test data
2. **Complete FPX Process**: Follow the BayarCash redirect to complete enrollment
3. **Verify Callback**: Check that the callback is received and processed
4. **Check Status**: Use the status checker to verify enrollment status

## Sample Test Data

```json
{
  "amount": 99.00,
  "payer_id_type": "NRIC",
  "payer_id": "901231015678",
  "payer_name": "Test User",
  "payer_email": "<EMAIL>",
  "payer_telephone_number": "0123456789",
  "frequency_mode": "MONTHLY",
  "application_reason": "Test subscription",
  "effective_date": "2025-01-10"
}
```

## Key Features

- ✅ **Self-contained**: Doesn't modify existing BayarCash integration
- ✅ **Secure**: Proper checksum validation and error handling
- ✅ **Testable**: Built-in testing interface and status checking
- ✅ **Documented**: Comprehensive inline comments and documentation
- ✅ **Configurable**: Uses existing BayarCash configuration
- ✅ **User-friendly**: Clean UI for enrollment and status checking

## Files Created

### Models
- `app/Models/Payment/BayarCashEMandate.php`

### Services
- `app/Services/BayarCashEMandateService.php`

### Controllers
- `app/Http/Controllers/Payment/BayarCashEMandateController.php`

### Views
- `resources/views/frontend/payment/bayarcash-emandate-enrollment.blade.php`
- `resources/views/frontend/payment/bayarcash-emandate-success.blade.php`
- `resources/views/frontend/payment/bayarcash-emandate-failed.blade.php`
- `resources/views/frontend/payment/bayarcash-emandate-status-checker.blade.php`
- `resources/views/frontend/payment/bayarcash-emandate-test.blade.php`

### Database
- `database/migrations/2025_01_02_000001_create_bayarcash_emandate_enrollments_table.php`

### Documentation
- `Docs/BayarCash_EMandate_Integration.md`

## Troubleshooting

### Common Issues

1. **Migration Error**: Ensure database connection is working
2. **Route Not Found**: Clear route cache with `php artisan route:clear`
3. **View Not Found**: Clear view cache with `php artisan view:clear`
4. **API Errors**: Check BayarCash credentials and sandbox settings

### Debug Mode

Enable debug logging in `config/logging.php` to see detailed error information.

### Support

- Check the comprehensive documentation in `Docs/BayarCash_EMandate_Integration.md`
- Review application logs for detailed error information
- Use the test interface at `/test/bayarcash-emandate` for verification

## Next Steps

1. Test the enrollment flow in sandbox environment
2. Verify callback handling works correctly
3. Test status checking functionality
4. Review and customize the UI as needed
5. Configure production credentials when ready to go live

The integration is now ready for testing and can be used alongside the existing BayarCash payment integration without any conflicts.
