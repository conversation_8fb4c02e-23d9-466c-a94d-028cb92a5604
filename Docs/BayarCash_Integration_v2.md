Here is an updated and improved Markdown file, integrating information from both the BayarCash e-mandate enrollment documentation and the BayarCash PHP SDK. This guide focuses on FPX Direct Debit and e-mandate enrollment for subscription payments, considering your Laravel 10 environment and existing API keys.

Markdown

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML``   # BayarCash Direct Debit & FPX e-Mandate Integration Guide (Laravel 10)  This guide provides a step-by-step approach to integrate BayarCash Direct Debit with a focus on FPX e-mandate enrollment for subscription payments using the BayarCash PHP SDK within a Laravel 10 environment. This aims to facilitate automatic renewals, moving away from one-time FPX payments.  ## 1. Prerequisites  * **BayarCash Account:** Your BayarCash account must be enabled for the Direct Debit payment channel.  * **Laravel 10 Project:** An existing Laravel 10 application.  * **API Keys:** Your BayarCash API Token and Secret Key are already stored in your `.env` file (e.g., `BAYARCASH_API_TOKEN`, `BAYARCASH_API_SECRET_KEY`).  * **Composer:** Composer installed for package management.  ## 2. BayarCash PHP SDK Installation  If you don't already have the SDK installed, you can add it to your Laravel project via Composer:  ```bash  composer require webimpian/bayarcash-php-sdk   ``

3\. SDK Configuration and Initialization (Laravel 10)
-----------------------------------------------------

The SDK provides an expressive interface for interacting with the BayarCash Payment Gateway API (supporting v2 and v3). For Laravel users, the SDK can be easily integrated by setting your API keys in the .env file.

In your Laravel application, you can initialize the SDK, typically within a controller or service, by resolving it from the service container:

PHP

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML`   use Webimpian\BayarcashSdk\Bayarcash;  // Assuming BAYARCASH_API_TOKEN is set in your .env  $bayarcash = app(Bayarcash::class);  // Optional: Set API version (v3 is recommended for enhanced features)  $bayarcash->setApiVersion('v3');  // Optional: Use sandbox environment for testing  // $bayarcash->useSandbox();   `

**Note:** Ensure your BAYARCASH\_API\_TOKEN and BAYARCASH\_API\_SECRET\_KEY are correctly configured in your .env file as the SDK will automatically pick them up when resolved through Laravel's service container.

4\. FPX Direct Debit e-Mandate Enrollment Process
-------------------------------------------------

The FPX Direct Debit feature within the SDK allows for the enrollment, maintenance, and termination of e-mandates. The enrollment process involves creating an enrollment intent and redirecting the payer.

### Step 4.1: Create FPX Direct Debit Enrollment Request Data

Prepare the necessary data for the e-mandate enrollment request. This will include mandatory parameters from the BayarCash API documentation and potentially optional ones like return\_url, success\_url, and failed\_url for callback handling.

PHP

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML`   $requestData = [      'portal_key' => env('BAYARCASH_PORTAL_KEY'), // Ensure you have this in your .env      'order_number' => 'SUB_' . uniqid(), // A unique order number for this enrollment      'amount' => 100.00, // The recurring subscription amount      'payer_id_type' => 'NRIC',      'payer_id' => '901231015678', // Example ID      'payer_name' => 'John Doe',      'payer_email' => '<EMAIL>',      'payer_telephone_number' => '0123456789',      'frequency_mode' => 'MONTHLY', // Or 'YEARLY', 'WEEKLY', etc. for subscription      'application_reason' => 'Subscription for Premium Service',      'effective_date' => now()->addDays(7)->format('Y-m-d'), // When the mandate becomes active      'return_url' => route('bayarcash.direct-debit.callback'), // Your callback URL      'success_url' => route('bayarcash.direct-debit.success'),      'failed_url' => route('bayarcash.direct-debit.failed'),      // 'metadata' => ['user_id' => $user->id], // Optional metadata  ];   `

**Important:** Define the routes (bayarcash.direct-debit.callback, bayarcash.direct-debit.success, bayarcash.direct-debit.failed) in your routes/web.php or routes/api.php file to handle the redirects and callbacks.

### Step 4.2: Generate Enrollment Checksum

It is highly recommended to use checksums for enhanced security. The SDK provides a method to generate this:

PHP

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML`   $checksum = $bayarcash->createPaymentIntenChecksumValue(      env('BAYARCASH_API_SECRET_KEY'),      $requestData  );  // Append the checksum to your request data  $requestData['checksum'] = $checksum;   `

### Step 4.3: Create FPX Direct Debit Enrollment Intent and Redirect

Now, use the SDK to create the FPX Direct Debit enrollment intent. This will return a URL to which you must redirect the payer to complete the enrollment process on the BayarCash/FPX gateway.

PHP

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML`   try {      $response = $bayarcash->createFpxDirectDebitEnrollmentIntent($requestData);      if (isset($response['payment_intent_url'])) {          // Redirect the user to the BayarCash enrollment page          return redirect()->away($response['payment_intent_url']);      } else {          // Handle error: payment_intent_url not found          Log::error('BayarCash enrollment URL not found', ['response' => $response]);          return redirect()->back()->with('error', 'Failed to initiate direct debit enrollment.');      }  } catch (\Exception $e) {      Log::error('BayarCash enrollment error: ' . $e->getMessage());      return redirect()->back()->with('error', 'An error occurred during direct debit enrollment.');  }   `

5\. Handling Callbacks and Webhooks (Crucial for Subscription)
--------------------------------------------------------------

After the user completes the enrollment on the BayarCash/FPX gateway, BayarCash will send callback data to your specified return\_url. This is where you receive the status of the e-mandate enrollment (successful, failed, pending).

### Step 5.1: Implement Callback Endpoint

Your return\_url (e.g., https://yourdomain.com/bayarcash/direct-debit/callback) should have a controller method to handle the incoming POST request from BayarCash.

PHP

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML`   // In your routes/web.php (or api.php if you prefer API-only callbacks)  Route::post('/bayarcash/direct-debit/callback', [BayarCashController::class, 'handleDirectDebitCallback'])->name('bayarcash.direct-debit.callback');  Route::get('/bayarcash/direct-debit/success', function () {      return view('direct-debit.success');  })->name('bayarcash.direct-debit.success');  Route::get('/bayarcash/direct-debit/failed', function () {      return view('direct-debit.failed');  })->name('bayarcash.direct-debit.failed');   `

PHP

Plain textANTLR4BashCC#CSSCoffeeScriptCMakeDartDjangoDockerEJSErlangGitGoGraphQLGroovyHTMLJavaJavaScriptJSONJSXKotlinLaTeXLessLuaMakefileMarkdownMATLABMarkupObjective-CPerlPHPPowerShell.propertiesProtocol BuffersPythonRRubySass (Sass)Sass (Scss)SchemeSQLShellSwiftSVGTSXTypeScriptWebAssemblyYAMLXML`   // In app/Http/Controllers/BayarCashController.php   `

`   namespace App\Http\Controllers;  use Illuminate\Http\Request;  use Illuminate\Support\Facades\Log;  use Webimpian\BayarcashSdk\Bayarcash; // Assuming you've included the SDK  class BayarCashController extends Controller  {      protected $bayarcash;      public function __construct(BayarCash $bayarcash)      {          $this->bayarcash = $bayarcash;      }      public function handleDirectDebitCallback(Request $request)      {          $callbackData = $request->all();          Log::info('BayarCash Direct Debit Callback Received', $callbackData);          // Verify the callback data using SDK's checksum verification          try {              $isCallbackVerified = $this->bayarcash->verifyReturnUrlCallback(                  env('BAYARCASH_API_SECRET_KEY'),                  $callbackData              );              if (!$isCallbackVerified) {                  Log::warning('BayarCash Callback Verification Failed', ['data' => $callbackData]);                  // Respond with an error or reject the callback if verification fails                  return response()->json(['status' => 'error', 'message' => 'Callback verification failed'], 403);              }              $orderNumber = $callbackData['order_number'] ?? null;              $status = $callbackData['status'] ?? null; // e.g., 'completed', 'failed', 'pending'              $transactionId = $callbackData['transaction_id'] ?? null; // Store this              // Find your subscription record by $orderNumber              // Update the subscription status based on $status              // If status is 'completed', the e-mandate is successfully set up.              // You can now mark the subscription as active and ready for recurring payments.              Log::info("BayarCash Direct Debit for Order {$orderNumber} status: {$status}");              // IMPORTANT: BayarCash expects a 200 OK response to acknowledge receipt of the callback.              return response()->json(['status' => 'success', 'message' => 'Callback received and processed']);          } catch (\Exception $e) {              Log::error('Error processing BayarCash Direct Debit callback: ' . $e->getMessage(), ['data' => $callbackData]);              return response()->json(['status' => 'error', 'message' => 'Internal server error'], 500);          }      }  }   `

### Step 5.2: Managing Subscription Status

Based on the status received in the callback, update your subscription records.

*   **completed**: The e-mandate is successfully established. You can activate the user's subscription and schedule future recurring charges.
    
*   **failed**: The enrollment failed. Notify the user and guide them to retry or choose an alternative payment method.
    
*   **pending**: The status is still being processed.
    

6\. Triggering Recurring Payments (Automatic Renewal)
-----------------------------------------------------

Once the e-mandate is successfully enrolled, BayarCash will typically handle the recurring debits based on the frequency\_mode and effective\_date you provided during enrollment. The BayarCash platform manages the recurring charges against the established mandate.

**Important:** The provided documentation primarily covers the _enrollment_ of the e-mandate. For precise details on how _your system_ explicitly triggers or confirms recurring debits for automatic renewal, you would need to consult BayarCash's comprehensive API documentation or their support. Often, for direct debit, the merchant initiates the _first_ payment, and subsequent payments are automatically handled by the direct debit system based on the mandate, or you might need to send a simple "charge" request with the mandate ID.

7\. Security Recommendations
----------------------------

*   **Always use Checksums:** Generate and verify checksums for all requests and callbacks. The SDK provides methods for this.
    
*   **Verify Callbacks:** Always verify the authenticity of callbacks using the provided SDK methods to prevent fraudulent notifications.
    
*   **Store Transaction IDs:** Store and validate transaction IDs to prevent duplicate processing.
    
*   **Use HTTPS:** Ensure all your API communications are over HTTPS.
    
*   **Secure API Keys:** Keep your API tokens and secret keys strictly confidential and out of public repositories. Your .env file approach is correct for this.
    

This updated guide provides a more robust foundation for your Laravel 10 integration, specifically incorporating FPX Direct Debit enrollment. For detailed information on subsequent recurring payment charges and advanced webhook handling for direct debit status updates, please refer to the official BayarCash API documentation or contact their support.

**Sources:**

*   BayarCash Direct Debit e-Mandate Enrollment Documentation: [https://api.webimpian.support/bayarcash/direct-debit/e-mandate-enrollment](https://api.webimpian.support/bayarcash/direct-debit/e-mandate-enrollment)
    
*   BayarCash PHP SDK GitHub Repository: [https://github.com/webimpian/bayarcash-php-sdk](https://github.com/webimpian/bayarcash-php-sdk)