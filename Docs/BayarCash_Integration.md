# BayarCash Payment Gateway Integration

This document provides an overview of the BayarCash Payment Gateway integration in BizappOS.

## Overview

The BayarCash Payment Gateway integration allows BizappOS to process payments through the BayarCash payment gateway. The integration uses the official BayarCash PHP SDK (`webimpian/bayarcash-php-sdk`) and supports all the key functionalities of the BayarCash API v3.

## Installation

1. Install the BayarCash PHP SDK via Composer:

```bash
composer require webimpian/bayarcash-php-sdk
```

2. Add the BayarCash configuration to your `.env` file:

```
BC_API_TOKEN=your_api_token_here
BC_API_SECRET_KEY=your_api_secret_key_here
BC_PORTAL_KEY=your_portal_key_here
BC_SANDBOX=true
BC_API_VERSION=v3
BC_BASE_URL=https://api.console.bayar.cash/v3/
BC_SANDBOX_BASE_URL=https://api.console.bayarcash-sandbox.com/v3/
```

3. Run the migration to create the BayarCash transactions table:

```bash
php artisan migrate
```

## Configuration

The BayarCash integration is configured in the `config/services.php` file:

```php
'bayarcash' => [
    'api_token' => env('BC_API_TOKEN'),
    'api_secret_key' => env('BC_API_SECRET_KEY'),
    'sandbox' => env('BC_SANDBOX', true),
    'api_version' => env('BC_API_VERSION', 'v3'),
    'base_url' => env('BC_BASE_URL', 'https://api.console.bayar.cash/v3/'),
    'sandbox_base_url' => env('BC_SANDBOX_BASE_URL', 'https://api.console.bayarcash-sandbox.com/v3/'),
]
```

> **Note:** The correct API endpoint URL for BayarCash v3 is `api.console.bayar.cash/v3/` for production and `api.console.bayarcash-sandbox.com/v3/` for sandbox environment.

## Usage

### Basic Initialization

```php
use Webimpian\Bayarcash\Bayarcash;

// Initialize the BayarCash SDK with your API token
$apiToken = config('services.bayarcash.api_token');
$bayarcash = new Bayarcash($apiToken);

// Set API version to v3
$bayarcash->setApiVersion('v3');

// Use sandbox environment for testing
if (config('services.bayarcash.sandbox', true)) {
    $bayarcash->useSandbox();
}
```

### Fetching Payment Portals

```php
// Get available payment portals
$response = $bayarcash->getPortals();

if ($response->success) {
    $portals = $response->data;
    // Process portals
}
```

### Fetching Payment Channels

```php
// Get available payment channels for a portal
$portalKey = 'your_portal_key';
$response = $bayarcash->getChannels($portalKey);

if ($response->success) {
    $channels = $response->data;
    // Process channels
}
```

### Creating a Payment Intent

```php
// Prepare data for payment intent
$data = [
    'portal_key' => 'your_portal_key',
    'order_number' => 'ORDER123',
    'amount' => 100.00,
    'payer_name' => 'John Doe',
    'payer_email' => '<EMAIL>',
    'payer_telephone_number' => '1234567890',
    'callback_url' => route('payment.bayarcash.callback'),
    'return_url' => route('payment.bayarcash.return'),
    'payment_channel' => 1  // Use integer value for payment channel (e.g., 1 for FPX)
];

// Generate checksum
$apiSecretKey = config('services.bayarcash.api_secret_key');
$checksum = generateChecksum($data, $apiSecretKey);
$data['checksum'] = $checksum;

// Create payment intent
$result = $bayarCashService->createPaymentIntent($data);

if ($result['success']) {
    // Redirect to payment URL
    return redirect($result['payment_url']);
}
```

### Generating Checksum

```php
function generateChecksum(array $data, string $apiSecretKey)
{
    // Create a copy of the data with only the required fields for the checksum
    $payloadData = [
        'payment_channel' => $data['payment_channel'],
        'order_number' => $data['order_number'],
        'amount' => $data['amount'],
        'payer_name' => $data['payer_name'],
        'payer_email' => $data['payer_email']
    ];

    // Sort data by key exactly as shown in the documentation
    ksort($payloadData);

    // Concatenate values with pipe delimiter
    $payloadString = implode('|', $payloadData);

    // Generate HMAC SHA-256 hash using the API secret key
    return hash_hmac('sha256', $payloadString, $apiSecretKey);
}
```

### Handling Callbacks

#### Pre-Transaction Callback

```php
// Verify pre-transaction callback data
$callbackData = request()->all();
$apiSecretKey = config('services.bayarcash.api_secret_key');
$isValid = $bayarcash->verifyPreTransactionCallbackData($callbackData, $apiSecretKey);

if ($isValid) {
    // Process pre-transaction callback
    return response()->json(['success' => true]);
}
```

#### Transaction Callback

```php
// Verify transaction callback data
$callbackData = request()->all();
$apiSecretKey = config('services.bayarcash.api_secret_key');
$isValid = $bayarcash->verifyTransactionCallbackData($callbackData, $apiSecretKey);

if ($isValid) {
    // Update transaction status
    // Process business logic based on successful payment
    return response()->json(['success' => true]);
}
```

#### Return URL Callback

```php
// Verify return URL callback data
$callbackData = request()->all();
$apiSecretKey = config('services.bayarcash.api_secret_key');
$isValid = $bayarcash->verifyReturnUrlCallbackData($callbackData, $apiSecretKey);

if ($isValid) {
    // Redirect to success page
    return redirect()->route('payment.success');
}
```

### Retrieving Transaction Details

```php
// Get payment intent details
$paymentIntentId = 'your_payment_intent_id';
$response = $bayarcash->getPaymentIntent($paymentIntentId);

if ($response->success) {
    $paymentIntent = $response->data;
    // Process payment intent details
}

// Get transactions by order number
$orderNumber = 'ORDER123';
$response = $bayarcash->getTransactionByOrderNumber($orderNumber);

// Get transactions by reference number
$referenceNumber = 'REF123';
$response = $bayarcash->getTransactionByReferenceNumber($referenceNumber);

// Get all transactions with optional filters
$filters = [
    'status' => 'completed',
    'date_from' => '2023-01-01',
    'date_to' => '2023-12-31'
];
$response = $bayarcash->getAllTransactions($filters);
```

## Security Best Practices

1. Always verify callback data using the appropriate verification methods.
2. Store API credentials securely in environment variables.
3. Use HTTPS for all callback and return URLs.
4. Log all payment-related activities for auditing and debugging.
5. Implement proper error handling for API requests.

## Example Implementation

For a complete example implementation, see the `BayarCashExampleController` in the `app/Http/Controllers/Examples` directory.

## Routes

The following routes are available for the BayarCash integration:

### Web Routes

```php
// BayarCash payment routes
Route::prefix('payment/bayarcash')->group(function () {
    Route::get('/portals', [BayarCashController::class, 'getPortals'])->name('payment.bayarcash.portals');
    Route::get('/channels', [BayarCashController::class, 'getChannels'])->name('payment.bayarcash.channels');
    Route::post('/create', [BayarCashController::class, 'createPaymentIntent'])->name('payment.bayarcash.create');
    Route::post('/pre-transaction-callback', [BayarCashController::class, 'handlePreTransactionCallback'])->name('payment.bayarcash.pre-callback');
    Route::post('/callback', [BayarCashController::class, 'handleTransactionCallback'])->name('payment.bayarcash.callback');
    Route::get('/return', [BayarCashController::class, 'handleReturnUrlCallback'])->name('payment.bayarcash.return');
    Route::get('/transaction', [BayarCashController::class, 'getTransactionDetails'])->name('payment.bayarcash.transaction');
    Route::get('/transactions', [BayarCashController::class, 'getAllTransactions'])->name('payment.bayarcash.transactions');
});
```

### API Routes

```php
// BayarCash API routes
Route::prefix('bayarcash')->group(function () {
    Route::get('/portals', [BayarCashController::class, 'getPortals']);
    Route::get('/channels', [BayarCashController::class, 'getChannels']);
    Route::post('/create', [BayarCashController::class, 'createPaymentIntent']);
    Route::post('/pre-transaction-callback', [BayarCashController::class, 'handlePreTransactionCallback']);
    Route::post('/callback', [BayarCashController::class, 'handleTransactionCallback']);
    Route::get('/transaction', [BayarCashController::class, 'getTransactionDetails']);
    Route::get('/transactions', [BayarCashController::class, 'getAllTransactions']);
});
```

## Response Handling

### Handling 201 Created Response

The BayarCash API returns a 201 Created response when a payment intent is successfully created. The response format is different from the standard success response:

```json
{
  "type": "payment_intent",
  "id": "pi_Dzod0q",
  "payer_name": "John Doe",
  "payer_email": "<EMAIL>",
  "payer_telephone_number": "60123456789",
  "order_number": "TEST123",
  "amount": 60,
  "url": "https://console.bayarcash-sandbox.com/payment-intent/pi_Dzod0q"
}
```

In your code, you should handle this response format specifically:

```php
// For 201 Created response, the API returns a different format
if ($statusCode === 201) {
    // Store transaction in database
    $transaction = BayarCash::create([
        'user_id' => $data['user_id'] ?? null,
        'company_id' => $data['company_id'] ?? null,
        'payment_intent_id' => $responseData->id ?? null,
        'order_number' => $data['order_number'],
        'portal_key' => $data['portal_key'],
        'payment_channel' => $data['payment_channel'] ?? null,
        'amount' => $data['amount'],
        'payer_name' => $data['payer_name'],
        'payer_email' => $data['payer_email'],
        'payer_phone' => $data['payer_telephone_number'] ?? null,
        'status' => 'pending',
        'payment_url' => $responseData->url ?? null,
        'metadata' => $data['metadata'] ?? null
    ]);

    return [
        'success' => true,
        'transaction' => $transaction,
        'payment_url' => $responseData->url,
        'payment_intent_id' => $responseData->id
    ];
}
```

## Support

For support with the BayarCash integration, please contact the BizappOS support team or refer to the [BayarCash API documentation](https://docs.bayarcash.my).
