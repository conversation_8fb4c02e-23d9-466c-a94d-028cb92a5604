<?php

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\API\SyncController;
use App\Http\Controllers\API\GkashController;
use App\Http\Controllers\API\OrderController;
use App\Http\Controllers\API\ReportController;
use App\Http\Controllers\auth\LoginController;
use App\Http\Controllers\API\v2\AuthController;
use App\Http\Controllers\API\v2\ShiftController;
use App\Http\Controllers\API\v2\DefaultController;
use App\Http\Controllers\API\v2\ProductController;
use App\Http\Controllers\API\v2\ProfileController;
use App\Http\Controllers\API\v2\CustomerController;
use App\Http\Controllers\API\v3\FileUploadController;
use App\Http\Controllers\MBI\API\DashboardController;
use App\Http\Controllers\API\v3\ClientLoggingController;

use App\Http\Controllers\API\v3\UserStatisticsController;
use App\Http\Controllers\API\BizappIntegrationsController;
use App\Http\Controllers\remote_configs\RemoteConfigsController;
use App\Http\Controllers\API\v3\AuthController as V3AuthController;
use App\Http\Controllers\API\v2\OrderController as V2OrderController;
use App\Http\Controllers\API\v3\CAPayController as V3CAPayController;
use App\Http\Controllers\API\v3\GkashController as V3GkashController;
use App\Http\Controllers\API\v3\OrderController as V3OrderController;
use App\Http\Controllers\API\v3\ShiftController as V3ShiftController;
use App\Http\Controllers\MBI\API\AuthController as MBIAuthController;
use App\Http\Controllers\API\v3\ReportController as V3ReportController;
use App\Http\Controllers\API\v3\DefaultController as V3DefaultController;

use App\Http\Controllers\API\v3\ProductController as V3ProductController;
use App\Http\Controllers\API\v3\ProfileController as V3ProfileController;
use App\Http\Controllers\API\v3\CustomerController as V3CustomerController;
use App\Http\Controllers\API\v3\MoneyOutController as V3MoneyOutController;
use App\Http\Controllers\MBI\API\DashboardController as MBIDashboardController;
use App\Http\Controllers\MBI\API\Merchant\MerchantController as MBIMerchantController;
use App\Http\Controllers\MBI\API\Statistic\StatisticsController as MBIStatisticsController;
use App\Http\Controllers\MBI\API\DashboardController_optimized as DashboardController_optimized;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

//Api V2
Route::prefix('v2')->group(function(){
    // for mobile app access
    Route::post('/prelogin', [AuthController::class,'prelogin']);

    Route::post('/login', [AuthController::class, 'login']);

    Route::middleware('auth:sanctum')->group(function(){ // Client error logging should be accessible without authentication

        Route::prefix('profile')->group(function(){
            Route::get('/', [ProfileController::class, 'index']);
            Route::post('/change-password', [ProfileController::class, 'changePassword']);
        });

        Route::prefix('shift')->group(function(){
            Route::get('/', [ShiftController::class, 'index']);
            Route::post('/start', [ShiftController::class, 'start']);
            Route::post('/end', [ShiftController::class, 'end']);
            Route::get('/check', [ShiftController::class, 'check']);
        });

        Route::prefix('product')->group(function(){
            Route::get('/', [ProductController::class, 'index']);
            Route::post('/set-favorite', [ProductController::class, 'setFavorite']);
            Route::post('/update-inventory', [ProductController::class, 'updateInventory']);
            Route::post('/sync-product', [ProductController::class, 'syncProduct']);
        });

        Route::prefix('customer')->group(function(){
            Route::get('/', [CustomerController::class, 'index']);
            Route::post('/add', [CustomerController::class, 'store']);
        });

        Route::prefix('order')->group(function(){
            Route::get('/', [V2OrderController::class, 'index']);
            Route::post('/create', [V2OrderController::class, 'store']);
            Route::post('/cancel', [V2OrderController::class, 'cancel']);
            Route::post('/cancel-order', [V2OrderController::class, 'cancelOrderV2']);
            Route::post('/edit-note', [V2OrderController::class, 'editNote']);
            Route::post('/createLocalQueue', [V2OrderController::class, 'storeFromLocalQueue']);

            // Order history
            Route::get('/order-history', [V2OrderController::class, 'getOrderHistory']);
            Route::get('/order-history/{orderid}', [V2OrderController::class, 'getOrderHistoryDetail']);

        });
    });
});

//Api V3
Route::prefix('v3')->group(function(){
    // for mobile app access
    Route::post('/prelogin', [V3AuthController::class,'prelogin']);

    //forgot password
    Route::post('forgot-password',[V3AuthController::class,'forgotPassword']);

    Route::post('/login', [V3AuthController::class, 'login']);

    // Client error logging endpoint
    Route::post('client_logging', [ClientLoggingController::class, 'store'])->middleware('api');

    Route::middleware('auth:sanctum')->group(function(){


        Route::prefix('profile')->group(function(){
            Route::get('/', [V3ProfileController::class, 'index']);
            Route::post('/change-password', [V3ProfileController::class, 'changePassword']);
        });

        Route::prefix('shift')->group(function(){
            Route::get('/', [V3ShiftController::class, 'index']);
            Route::post('/start', [V3ShiftController::class, 'start']);
            Route::post('/end', [V3ShiftController::class, 'end']);
            Route::get('/check', [V3ShiftController::class, 'check']);
        });

        Route::prefix('product')->group(function(){
            Route::get('/', [V3ProductController::class, 'index']);
            Route::get('/all', [V3ProductController::class, 'getAllProduct']);
            Route::post('/create', [V3ProductController::class, 'addNewProduct']);
            Route::post('/set-favorite', [V3ProductController::class, 'setFavorite']);
            Route::post('/update-inventory', [V3ProductController::class, 'updateInventory']);
            Route::post('/sync-product', [V3ProductController::class, 'syncProduct']);
            Route::get('/collections', [V3ProductController::class, 'getCollections']);
            Route::get('/collection-products', [V3ProductController::class, 'getProductCollections']);
            Route::get('/check-sku', [V3ProductController::class, 'checkSKU']);

        });

        Route::prefix('customer')->group(function(){
            Route::get('/', [V3CustomerController::class, 'index']);
            Route::post('/add', [V3CustomerController::class, 'store']);
        });

        Route::prefix('report')->group(function(){
            Route::get('/overview', [V3ReportController::class, 'index']);
            Route::get('/details', [V3ReportController::class, 'details']);
            Route::get('/monthly-pdf', [V3ReportController::class, 'downloadMonthlyPDF']);
              // Money Out Reports
              Route::prefix('money-out')->group(function(){
                Route::get('/', [V3MoneyOutController::class, 'index']);
                Route::post('/add', [V3MoneyOutController::class, 'store']);
                Route::put('/{id}', [V3MoneyOutController::class, 'update']);
                Route::delete('/{id}', [V3MoneyOutController::class, 'destroy']);
                Route::get('/profit-loss', [V3MoneyOutController::class, 'profitAndLoss']);
                Route::get('/profit-loss-pdf', [V3MoneyOutController::class, 'downloadProfitLossPDF']);
                Route::get('/daily-summary', [V3MoneyOutController::class, 'dailySummary']);
                Route::get('/daily-transactions', [V3MoneyOutController::class, 'dailyTransactions']);
                Route::get('/{id}/receipt', [V3MoneyOutController::class, 'serveReceipt']);

            });
        
        });

        Route::prefix('order')->group(function(){
            Route::get('/', [V3OrderController::class, 'index']);
            Route::post('/create', [V3OrderController::class, 'store']);
            Route::post('/cancel', [V3OrderController::class, 'cancel']);
            Route::post('/cancel-order', [V3OrderController::class, 'cancelOrderV3']);
            Route::post('/edit-note', [V3OrderController::class, 'editNote']);
            Route::post('/createLocalQueue', [V3OrderController::class, 'storeFromLocalQueue']);
            Route::post('/send-receipt',[V3OrderController::class,'getReceiptImage']);

            // Order history
            Route::get('/order-history', [V3OrderController::class, 'getOrderHistory']);
            // history-all will include deleted ones and orders made by staff
            Route::get('/order-history-all', [V3OrderController::class, 'getOrderHistoryAll']);
            Route::get('/order-history/{orderid}', [V3OrderController::class, 'getOrderHistoryDetail']);
        });

        Route::prefix('gkash')->group(function(){
            // Route::post('callback',[V3GkashController::class,'gkashCallback'])->name('v3.gCallback');
            Route::post('checkPaymentStatus',[V3GkashController::class,'gkashCheckPaymentStatus'])->name('v3.gPaymentStatus');
            Route::post('refund',[V3GkashController::class,'gkashRefund'])->name('v3.gRefund');
            Route::post('updateRef',[V3GkashController::class,'gkashUpdateReference'])->name('v3.gUpdateRef');
            Route::post('updateExtraStatus',[V3GkashController::class,'updateExtrasToStatus'])->name('v3.gUpdateExtra');

            // bizappay / gkash registration
            Route::post('registrationProcess',[V3GkashController::class,'getRegistrationInfo'])->name('v3.gRegisterProcess');
            Route::post('registrationCallback', [V3GkashController::class,'bizappayCallback'])->name('v3.api.payment.done');
            Route::post('registerPaymentStatus',[V3GkashController::class,'checkBillCodeStatus'])->name('v3.check.payment.register');
        });

        Route::prefix('cap')->group(function(){
            Route::post('login',[V3CAPayController::class,'capLogin']);
            Route::post('callback',[V3CAPayController::class,'capCallback'])->name('v3.capCallback');
            Route::get('webReturn',[V3CAPayController::class,'capLogin'])->name('v3.capWebReturn');
            Route::post('requestPayment',[V3CAPayController::class,'requestPayment']);
            Route::post('refund',[V3CAPayController::class,'refund']);
            Route::get('checkRefundStatus',[V3CAPayController::class,'checkRefundStatus']);
        });
    });
    Route::get('queryPayment',[V3CAPayController::class,'queryPayment']);

    Route::prefix('gkash')->group(function(){
        Route::post('callback',[V3GkashController::class,'gkashCallback'])->name('v3.gCallback');
    });

    // User Statistics API
    Route::prefix('statistics')->group(function(){
        Route::get('/users', [UserStatisticsController::class, 'getUserStatistics']);
        Route::get('/users-optimized', [UserStatisticsController::class, 'getOptimizedUserStatistics']);
        Route::get('/users-optimized/status', [UserStatisticsController::class, 'getJobStatus']);
        Route::get('/users-optimized/debug-queue', [UserStatisticsController::class, 'debugQueue']);
    });

    Route::prefix('order')->group(function(){
        Route::get('/receipt/{orderid}/pdf', [V3OrderController::class, 'generatePDFPublic']);
    });
});

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// retrieve all products of plats users
Route::get('getPlatsUserAndProducts', [Controller::class, 'getPlatsUserAndProducts']);

// process bulk upload user registration
Route::post('process-bulk-register', [FileUploadController::class, 'processFileUpload']);

// remote configs
Route::post('updateBizappXios',[RemoteConfigsController::class,'updateBizappXios'])->name('updateBizappXios');
Route::post('updateBizappXandroid',[RemoteConfigsController::class,'updateBizappXAndroid'])->name('updateBizappXandroid');
Route::post('getCurrentVersionBizappMobile',[RemoteConfigsController::class,'getCurrentVersionBizappMobile'])->name('getCurrentVersionBizappX');


// save order from Bizapp web pos
Route::post('saveFromBizappWeb',[OrderController::class,'saveFromBizappWeb']);


// gkash transactions
Route::post('gkashCallback',[GkashController::class,'gkashCallback'])->name('gCallback');
Route::post('gkashCheckPaymentStatus',[GkashController::class,'gkashCheckPaymentStatus'])->name('gPaymentStatus');
Route::post('gkashRefund',[GkashController::class,'gkashRefund'])->name('gRefund');
Route::post('gkashUpdateRef',[GkashController::class,'gkashUpdateReference'])->name('gUpdateRef');
Route::post('gkashUpdateExtraStatus',[GkashController::class,'updateExtrasToStatus'])->name('gUpdateExtra');

// BayarCash API routes
Route::prefix('bayarcash')->group(function () {
    Route::get('/portals', [\App\Http\Controllers\Payment\BayarCashController::class, 'getPortals']);
    Route::get('/channels', [\App\Http\Controllers\Payment\BayarCashController::class, 'getChannels']);
    Route::post('/create', [\App\Http\Controllers\Payment\BayarCashController::class, 'createPaymentIntent']);
    Route::post('/pre-transaction-callback', [\App\Http\Controllers\Payment\BayarCashController::class, 'handlePreTransactionCallback']);
    Route::post('/callback', [\App\Http\Controllers\Payment\BayarCashController::class, 'handleTransactionCallback']);
    Route::get('/transaction', [\App\Http\Controllers\Payment\BayarCashController::class, 'getTransactionDetails']);
    Route::get('/transactions', [\App\Http\Controllers\Payment\BayarCashController::class, 'getAllTransactions']);
});

// BayarCash E-Mandate API routes
Route::prefix('bayarcash-emandate')->group(function () {
    Route::post('/create', [\App\Http\Controllers\Payment\BayarCashEMandateController::class, 'createEnrollmentIntent']);
    Route::post('/callback', [\App\Http\Controllers\Payment\BayarCashEMandateController::class, 'handleEnrollmentCallback']);
    Route::post('/check-status', [\App\Http\Controllers\Payment\BayarCashEMandateController::class, 'checkEnrollmentStatus']);
    Route::get('/my-enrollments', [\App\Http\Controllers\Payment\BayarCashEMandateController::class, 'getUserEnrollments']);
});

// bizappay / gkash registration
Route::post('gkashRegistrationProcess',[GkashController::class,'getRegistrationInfo'])->name('gRegisterProcess');
Route::post('gkashRegistrationCallback', [GkashController::class,'bizappayCallback'])->name('api.payment.done');
Route::post('checkRegisterPaymentStatus',[GkashController::class,'checkBillCodeStatus'])->name('check.payment.register');

Route::post('remoteLogin',[LoginController::class,'remoteLogin'])->name('remote.login');



// save order and orderDetails from app
Route::post('saveOrder',[OrderController::class, 'saveOrder'])->name('saveOrder');
Route::post('cancelOrder',[OrderController::class, 'cancelOrder'])->name('cancelOrder');
Route::post('cancelOrderFromBizapp',[V3OrderController::class,'cancelOrderFromBizapp'])->name('cancelOrderBizapp');

// reporting
Route::post('getTrxPayMethodToday',[OrderController::class, 'getTrxPayMethodToday'])->name('getTrxPayMethodToday');
Route::post('getTrxPayMethodDateRange',[OrderController::class, 'getTrxPayMethodDateRange'])->name('getTrxPayMethodDateRange');
Route::post('getTrxPayMethodTodayAll',[OrderController::class, 'getTrxPayMethodTodayAll'])->name('getTrxPayMethodTodayAll');
Route::post('getTrxPayMethodDateRangeAll',[OrderController::class, 'getTrxPayMethodDateRangeAll'])->name('getTrxPayMethodDateRangeAll');

// from Bizapp
// Route::post('stockUpdateFromBizapp',[V3OrderController::class, 'stockUpdateFromBizapp']);
Route::post('stockUpdateFromBizappv31',[V3OrderController::class, 'stockUpdateFromBizappV31']);
Route::post('stockUpdateFromBizappv31_BULK',[V3OrderController::class, 'stockUpdateFromBizappV31Bulk']);
Route::post('updateCollectionFromBizapp',[BizappIntegrationsController::class, 'updateCollectionFromBizapp']);

Route::post('syncAllOrdersFromBizapp',[SyncController::class, 'SyncAllOrdersFromBizapp']);
Route::post('syncCustomerFromPid',[SyncController::class, 'customerSyncByPid']);

Route::post('appsErrorLog',[DefaultController::class,'getAppsErrorLog'])->name('app.log');


// Auditing
Route::get('audit-collection-product-ownership', [V3ProductController::class, 'auditCollectionProductOwnership']);
Route::post('audit-collection-fix-mismatches/{companyId}', [V3ProductController::class, 'fixCollectionProductMismatches'])
        ->middleware(['auth:sanctum'])  // ensure authenticated
        ->name('collection.fix-mismatches');

 // MBI
 Route::prefix('mbi')->group(function(){
    Route::post('/login', [MBIAuthController::class, 'storeLogin'])->middleware('no-cache');

    // Apply middleware directly to the group
   Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('/report-sales', [MBIDashboardController::class, 'dataSales']);
        Route::get('/statistic-active', [DashboardController_optimized::class, 'statisticActive']);
        Route::get('/graph', [DashboardController_optimized::class, 'graph']);
        Route::get('/company-sales', [DashboardController_optimized::class, 'companySales']);
        Route::get('/top-products-sold', [MBIDashboardController::class, 'topProductsSold']);
        Route::get('/top-users-sales', [MBIDashboardController::class, 'topUsersBySales']);
        Route::get('/sales-by-hour', [MBIDashboardController::class, 'salesByHour']);
        Route::get('/active-users-rfm', [MBIDashboardController::class, 'activeUsersRFM']);

       // statistics
       Route::prefix('statistics')->group(function(){
        Route::get('/index', [MBIStatisticsController::class, 'statistics']);
        });

       // merchant details
       Route::prefix('merchant')->group(function(){
        Route::get('index', [MBIMerchantController::class, 'index']);
        Route::get('sales', [MBIMerchantController::class, 'sales']);

    });

       // logout
       Route::get('/logout', [MBIAuthController::class, 'logout']);
   });


});
