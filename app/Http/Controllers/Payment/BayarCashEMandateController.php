<?php

namespace App\Http\Controllers\Payment;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use App\Models\Payment\BayarCashEMandate;
use App\Services\BayarCashEMandateService;

/**
 * BayarCash E-Mandate Controller
 * 
 * This controller handles FPX Direct Debit e-mandate enrollment and management.
 * It provides endpoints for creating enrollment intents, handling callbacks,
 * and managing e-mandate status.
 */
class BayarCashEMandateController extends Controller
{
    /**
     * @var BayarCashEMandateService
     */
    protected $eMandateService;

    /**
     * Constructor
     */
    public function __construct(BayarCashEMandateService $eMandateService)
    {
        $this->eMandateService = $eMandateService;
    }

    /**
     * Show the e-mandate enrollment form
     * 
     * @return \Illuminate\View\View
     */
    public function showEnrollmentForm()
    {
        return view('frontend.payment.bayarcash-emandate-enrollment', [
            'frequencyOptions' => BayarCashEMandate::getFrequencyModeOptions(),
            'payerIdTypeOptions' => BayarCashEMandate::getPayerIdTypeOptions(),
            'portalKey' => config('services.bayarcash.portal_key')
        ]);
    }

    /**
     * Create an e-mandate enrollment intent
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     */
    public function createEnrollmentIntent(Request $request)
    {
        try {
            // Validate the request
            $request->validate([
                'amount' => 'required|numeric|min:1',
                'payer_id_type' => 'required|string|in:NRIC,PASSPORT,COMPANY',
                'payer_id' => 'required|string|max:50',
                'payer_name' => 'required|string|max:255',
                'payer_email' => 'required|email|max:255',
                'payer_telephone_number' => 'required|string|max:20',
                'frequency_mode' => 'required|string|in:WEEKLY,MONTHLY,QUARTERLY,YEARLY',
                'application_reason' => 'required|string|max:500',
                'effective_date' => 'required|date|after:today',
                'expiry_date' => 'nullable|date|after:effective_date',
                'metadata' => 'nullable|array'
            ]);

            // Prepare enrollment data
            $data = [
                'portal_key' => config('services.bayarcash.portal_key'),
                'amount' => $request->amount,
                'payer_id_type' => $request->payer_id_type,
                'payer_id' => $request->payer_id,
                'payer_name' => $request->payer_name,
                'payer_email' => $request->payer_email,
                'payer_telephone_number' => $request->payer_telephone_number,
                'frequency_mode' => $request->frequency_mode,
                'application_reason' => $request->application_reason,
                'effective_date' => $request->effective_date,
                'expiry_date' => $request->expiry_date,
                'return_url' => route('payment.bayarcash-emandate.callback'),
                'success_url' => route('payment.bayarcash-emandate.success'),
                'failed_url' => route('payment.bayarcash-emandate.failed'),
                'metadata' => $request->metadata
            ];

            // Add authenticated user and company if available
            if (auth()->check()) {
                $data['user_id'] = auth()->id();
                
                if (auth()->user()->company) {
                    $data['company_id'] = auth()->user()->company->id;
                }
            }

            // Create enrollment intent
            $result = $this->eMandateService->createEnrollmentIntent($data);

            if (!$result['success']) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $result['message']
                    ], 500);
                }

                return redirect()->back()
                    ->withErrors(['enrollment' => $result['message']])
                    ->withInput();
            }

            // Log successful enrollment creation
            Log::info('E-mandate enrollment intent created successfully', [
                'enrollment_id' => $result['data']['enrollment_id'],
                'order_number' => $result['data']['order_number'],
                'user_id' => auth()->id() ?? 'guest'
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'data' => $result['data']
                ]);
            }

            // Redirect to BayarCash enrollment page
            return redirect()->away($result['data']['enrollment_url']);

        } catch (Exception $e) {
            Log::error('Error creating e-mandate enrollment intent', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->withErrors(['enrollment' => 'An error occurred while creating the enrollment.'])
                ->withInput();
        }
    }

    /**
     * Handle e-mandate enrollment callback from BayarCash
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleEnrollmentCallback(Request $request)
    {
        try {
            $callbackData = $request->all();
            
            Log::info('E-mandate enrollment callback received', [
                'order_number' => $callbackData['order_number'] ?? 'unknown',
                'status' => $callbackData['status'] ?? 'unknown'
            ]);

            // Process the callback
            $result = $this->eMandateService->handleEnrollmentCallback($callbackData);

            if (!$result['success']) {
                Log::warning('E-mandate callback processing failed', [
                    'message' => $result['message'],
                    'data' => $callbackData
                ]);

                return response()->json([
                    'status' => 'error',
                    'message' => $result['message']
                ], 400);
            }

            Log::info('E-mandate callback processed successfully', [
                'order_number' => $callbackData['order_number'] ?? 'unknown',
                'status' => $result['data']['status'] ?? 'unknown'
            ]);

            // BayarCash expects a 200 OK response to acknowledge receipt of the callback
            return response()->json([
                'status' => 'success',
                'message' => 'Callback received and processed'
            ]);

        } catch (Exception $e) {
            Log::error('Error processing e-mandate enrollment callback', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $request->all()
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Internal server error'
            ], 500);
        }
    }

    /**
     * Handle successful e-mandate enrollment return
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function handleSuccessReturn(Request $request)
    {
        $orderNumber = $request->get('order_number');
        $enrollment = null;

        if ($orderNumber) {
            $enrollment = $this->eMandateService->getEnrollmentByOrderNumber($orderNumber);
        }

        return view('frontend.payment.bayarcash-emandate-success', [
            'enrollment' => $enrollment,
            'orderNumber' => $orderNumber
        ]);
    }

    /**
     * Handle failed e-mandate enrollment return
     * 
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function handleFailedReturn(Request $request)
    {
        $orderNumber = $request->get('order_number');
        $enrollment = null;

        if ($orderNumber) {
            $enrollment = $this->eMandateService->getEnrollmentByOrderNumber($orderNumber);
        }

        return view('frontend.payment.bayarcash-emandate-failed', [
            'enrollment' => $enrollment,
            'orderNumber' => $orderNumber,
            'errorMessage' => $request->get('error_message', 'E-mandate enrollment failed')
        ]);
    }

    /**
     * Check e-mandate enrollment status
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkEnrollmentStatus(Request $request)
    {
        try {
            $request->validate([
                'order_number' => 'required|string'
            ]);

            $enrollment = $this->eMandateService->getEnrollmentByOrderNumber($request->order_number);

            if (!$enrollment) {
                return response()->json([
                    'success' => false,
                    'message' => 'Enrollment not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'enrollment_id' => $enrollment->id,
                    'order_number' => $enrollment->order_number,
                    'status' => $enrollment->status,
                    'status_message' => $enrollment->status_message,
                    'amount' => $enrollment->amount,
                    'frequency_mode' => $enrollment->frequency_mode,
                    'effective_date' => $enrollment->effective_date,
                    'next_payment_at' => $enrollment->next_payment_at,
                    'enrolled_at' => $enrollment->enrolled_at,
                    'is_active' => $enrollment->isActive(),
                    'is_pending' => $enrollment->isPending(),
                    'has_failed' => $enrollment->hasFailed()
                ]
            ]);

        } catch (Exception $e) {
            Log::error('Error checking e-mandate enrollment status', [
                'error' => $e->getMessage(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's e-mandate enrollments
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserEnrollments(Request $request)
    {
        try {
            if (!auth()->check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required'
                ], 401);
            }

            $enrollments = $this->eMandateService->getActiveEnrollmentsForUser(auth()->id());

            return response()->json([
                'success' => true,
                'data' => $enrollments->map(function ($enrollment) {
                    return [
                        'enrollment_id' => $enrollment->id,
                        'order_number' => $enrollment->order_number,
                        'status' => $enrollment->status,
                        'amount' => $enrollment->amount,
                        'frequency_mode' => $enrollment->frequency_mode,
                        'effective_date' => $enrollment->effective_date,
                        'next_payment_at' => $enrollment->next_payment_at,
                        'enrolled_at' => $enrollment->enrolled_at,
                        'application_reason' => $enrollment->application_reason
                    ];
                })
            ]);

        } catch (Exception $e) {
            Log::error('Error getting user e-mandate enrollments', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show e-mandate status checking interface
     * 
     * @return \Illuminate\View\View
     */
    public function showStatusChecker()
    {
        return view('frontend.payment.bayarcash-emandate-status-checker');
    }
}
