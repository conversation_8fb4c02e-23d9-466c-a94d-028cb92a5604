@extends('layouts.auth')

@section('title', 'BayarCash E-Mandate Testing')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow">
                <div class="card-header bg-dark text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-flask me-2"></i>
                        BayarCash E-Mandate Integration Testing
                    </h4>
                    <p class="mb-0 mt-2">Test and verify the FPX Direct Debit e-mandate functionality</p>
                </div>
                
                <div class="card-body">
                    <!-- Integration Status -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-info-circle me-2"></i>Integration Status
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-body">
                                    <h6 class="card-title text-success">
                                        <i class="fas fa-check-circle me-2"></i>Components Installed
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><i class="fas fa-check text-success me-2"></i>E-Mandate Model</li>
                                        <li><i class="fas fa-check text-success me-2"></i>E-Mandate Service</li>
                                        <li><i class="fas fa-check text-success me-2"></i>E-Mandate Controller</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Database Migration</li>
                                        <li><i class="fas fa-check text-success me-2"></i>Routes & Views</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-info">
                                        <i class="fas fa-cog me-2"></i>Configuration
                                    </h6>
                                    <ul class="list-unstyled mb-0">
                                        <li><strong>Environment:</strong> {{ config('services.bayarcash.sandbox') ? 'Sandbox' : 'Production' }}</li>
                                        <li><strong>API Version:</strong> {{ config('services.bayarcash.api_version') }}</li>
                                        <li><strong>Portal Key:</strong> {{ config('services.bayarcash.portal_key') ? 'Configured' : 'Not Set' }}</li>
                                        <li><strong>API Token:</strong> {{ config('services.bayarcash.api_token') ? 'Configured' : 'Not Set' }}</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Actions -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-play me-2"></i>Test Actions
                            </h5>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-plus-circle fa-2x text-primary mb-3"></i>
                                    <h6 class="card-title">Create E-Mandate</h6>
                                    <p class="card-text">Test the e-mandate enrollment process with sample data.</p>
                                    <a href="{{ route('payment.bayarcash-emandate.enrollment') }}" class="btn btn-primary">
                                        Start Enrollment
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-search fa-2x text-info mb-3"></i>
                                    <h6 class="card-title">Check Status</h6>
                                    <p class="card-text">Verify enrollment status using order number.</p>
                                    <a href="{{ route('payment.bayarcash-emandate.status-checker') }}" class="btn btn-info">
                                        Check Status
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-list fa-2x text-success mb-3"></i>
                                    <h6 class="card-title">View Enrollments</h6>
                                    <p class="card-text">See all e-mandate enrollments for current user.</p>
                                    @auth
                                        <a href="{{ route('payment.bayarcash-emandate.my-enrollments') }}" class="btn btn-success">
                                            My Enrollments
                                        </a>
                                    @else
                                        <button class="btn btn-success" disabled>
                                            Login Required
                                        </button>
                                    @endauth
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- API Testing -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-code me-2"></i>API Testing
                            </h5>
                        </div>
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Available API Endpoints</h6>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>Method</th>
                                                    <th>Endpoint</th>
                                                    <th>Description</th>
                                                    <th>Auth Required</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><span class="badge bg-success">POST</span></td>
                                                    <td><code>/api/bayarcash-emandate/create</code></td>
                                                    <td>Create enrollment intent</td>
                                                    <td>Optional</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="badge bg-warning">POST</span></td>
                                                    <td><code>/api/bayarcash-emandate/callback</code></td>
                                                    <td>Handle BayarCash callbacks</td>
                                                    <td>No</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="badge bg-info">POST</span></td>
                                                    <td><code>/api/bayarcash-emandate/check-status</code></td>
                                                    <td>Check enrollment status</td>
                                                    <td>No</td>
                                                </tr>
                                                <tr>
                                                    <td><span class="badge bg-primary">GET</span></td>
                                                    <td><code>/api/bayarcash-emandate/my-enrollments</code></td>
                                                    <td>Get user enrollments</td>
                                                    <td>Yes</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sample Data -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-database me-2"></i>Sample Test Data
                            </h5>
                        </div>
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">Use this data for testing enrollment:</h6>
                                    <pre class="bg-light p-3 rounded"><code>{
  "amount": 99.00,
  "payer_id_type": "NRIC",
  "payer_id": "901231015678",
  "payer_name": "Test User",
  "payer_email": "<EMAIL>",
  "payer_telephone_number": "0123456789",
  "frequency_mode": "MONTHLY",
  "application_reason": "Test subscription for premium service",
  "effective_date": "{{ date('Y-m-d', strtotime('+7 days')) }}"
}</code></pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Documentation -->
                    <div class="row">
                        <div class="col-12">
                            <h5 class="text-primary border-bottom pb-2">
                                <i class="fas fa-book me-2"></i>Documentation & Resources
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-file-alt me-2"></i>Integration Guide
                                    </h6>
                                    <p class="card-text">Complete documentation for the e-mandate integration.</p>
                                    <a href="/docs/BayarCash_EMandate_Integration.md" class="btn btn-outline-primary btn-sm" target="_blank">
                                        View Documentation
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-external-link-alt me-2"></i>BayarCash API
                                    </h6>
                                    <p class="card-text">Official BayarCash API documentation and support.</p>
                                    <a href="https://api.webimpian.support/bayarcash/" class="btn btn-outline-secondary btn-sm" target="_blank">
                                        API Docs
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Important Notes -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle me-2"></i>Important Testing Notes</h6>
                                <ul class="mb-0">
                                    <li>Ensure you're using sandbox environment for testing</li>
                                    <li>Run database migrations before testing: <code>php artisan migrate</code></li>
                                    <li>Verify BayarCash API credentials are properly configured</li>
                                    <li>Check application logs for detailed error information</li>
                                    <li>Test callback URLs must be accessible from BayarCash servers</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
