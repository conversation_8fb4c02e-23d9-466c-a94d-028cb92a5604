@extends('layouts.auth')

@section('title', 'E-Mandate Enrollment Failed')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-danger text-white text-center">
                    <div class="mb-3">
                        <i class="fas fa-times-circle fa-3x"></i>
                    </div>
                    <h4 class="mb-0">E-Mandate Enrollment Failed</h4>
                </div>
                
                <div class="card-body text-center">
                    <div class="alert alert-danger">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>Enrollment Unsuccessful</h5>
                        <p class="mb-0">Unfortunately, your FPX Direct Debit e-mandate enrollment could not be completed.</p>
                    </div>

                    @if($errorMessage)
                        <div class="alert alert-warning">
                            <strong>Error Details:</strong><br>
                            {{ $errorMessage }}
                        </div>
                    @endif

                    @if($enrollment)
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Enrollment Details</h6>
                            </div>
                        </div>
                        
                        <div class="row text-start">
                            <div class="col-sm-6">
                                <strong>Order Number:</strong>
                            </div>
                            <div class="col-sm-6">
                                <code>{{ $enrollment->order_number }}</code>
                            </div>
                        </div>
                        
                        <div class="row text-start">
                            <div class="col-sm-6">
                                <strong>Amount:</strong>
                            </div>
                            <div class="col-sm-6">
                                RM {{ number_format($enrollment->amount, 2) }}
                            </div>
                        </div>
                        
                        <div class="row text-start">
                            <div class="col-sm-6">
                                <strong>Frequency:</strong>
                            </div>
                            <div class="col-sm-6">
                                {{ ucfirst(strtolower($enrollment->frequency_mode)) }}
                            </div>
                        </div>
                        
                        <div class="row text-start mb-4">
                            <div class="col-sm-6">
                                <strong>Status:</strong>
                            </div>
                            <div class="col-sm-6">
                                <span class="badge bg-danger">{{ ucfirst($enrollment->status) }}</span>
                            </div>
                        </div>

                        @if($enrollment->status_message)
                            <div class="alert alert-info">
                                <strong>Status Message:</strong><br>
                                {{ $enrollment->status_message }}
                            </div>
                        @endif
                    @else
                        @if($orderNumber)
                            <div class="row mb-4">
                                <div class="col-12">
                                    <p><strong>Order Number:</strong> <code>{{ $orderNumber }}</code></p>
                                </div>
                            </div>
                        @endif
                    @endif

                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb me-2"></i>What Can You Do?</h6>
                        <ul class="text-start mb-0">
                            <li>Check your bank account details and try again</li>
                            <li>Ensure your bank account has sufficient funds</li>
                            <li>Contact your bank to verify if there are any restrictions</li>
                            <li>Try using a different bank account</li>
                            <li>Contact our support team if the problem persists</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="{{ route('payment.bayarcash-emandate.enrollment') }}" class="btn btn-primary me-md-2">
                            <i class="fas fa-redo me-2"></i>Try Again
                        </a>
                        <a href="{{ route('payment.bayarcash-emandate.status-checker') }}" class="btn btn-outline-primary me-md-2">
                            <i class="fas fa-search me-2"></i>Check Status
                        </a>
                        <a href="{{ url('/') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
