@extends('layouts.auth')

@section('title', 'E-Mandate Enrollment Successful')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <div class="mb-3">
                        <i class="fas fa-check-circle fa-3x"></i>
                    </div>
                    <h4 class="mb-0">E-Mandate Enrollment Successful!</h4>
                </div>
                
                <div class="card-body text-center">
                    <div class="alert alert-success">
                        <h5><i class="fas fa-thumbs-up me-2"></i>Congratulations!</h5>
                        <p class="mb-0">Your FPX Direct Debit e-mandate has been successfully enrolled.</p>
                    </div>

                    @if($enrollment)
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary border-bottom pb-2">Enrollment Details</h6>
                            </div>
                        </div>
                        
                        <div class="row text-start">
                            <div class="col-sm-6">
                                <strong>Order Number:</strong>
                            </div>
                            <div class="col-sm-6">
                                <code>{{ $enrollment->order_number }}</code>
                            </div>
                        </div>
                        
                        <div class="row text-start">
                            <div class="col-sm-6">
                                <strong>Amount:</strong>
                            </div>
                            <div class="col-sm-6">
                                RM {{ number_format($enrollment->amount, 2) }}
                            </div>
                        </div>
                        
                        <div class="row text-start">
                            <div class="col-sm-6">
                                <strong>Frequency:</strong>
                            </div>
                            <div class="col-sm-6">
                                {{ ucfirst(strtolower($enrollment->frequency_mode)) }}
                            </div>
                        </div>
                        
                        <div class="row text-start">
                            <div class="col-sm-6">
                                <strong>Effective Date:</strong>
                            </div>
                            <div class="col-sm-6">
                                {{ $enrollment->effective_date->format('d M Y') }}
                            </div>
                        </div>
                        
                        @if($enrollment->next_payment_at)
                            <div class="row text-start">
                                <div class="col-sm-6">
                                    <strong>Next Payment:</strong>
                                </div>
                                <div class="col-sm-6">
                                    {{ $enrollment->next_payment_at->format('d M Y') }}
                                </div>
                            </div>
                        @endif
                        
                        <div class="row text-start mb-4">
                            <div class="col-sm-6">
                                <strong>Status:</strong>
                            </div>
                            <div class="col-sm-6">
                                <span class="badge bg-success">{{ ucfirst($enrollment->status) }}</span>
                            </div>
                        </div>
                    @else
                        @if($orderNumber)
                            <div class="row mb-4">
                                <div class="col-12">
                                    <p><strong>Order Number:</strong> <code>{{ $orderNumber }}</code></p>
                                </div>
                            </div>
                        @endif
                    @endif

                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>What's Next?</h6>
                        <ul class="text-start mb-0">
                            <li>Your e-mandate is now active and ready for recurring payments</li>
                            <li>The first payment will be processed on the effective date</li>
                            <li>You will receive email notifications before each payment</li>
                            <li>You can manage your e-mandates from your account dashboard</li>
                        </ul>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="{{ route('payment.bayarcash-emandate.status-checker') }}" class="btn btn-outline-primary me-md-2">
                            <i class="fas fa-search me-2"></i>Check Status
                        </a>
                        @auth
                            <a href="{{ route('payment.bayarcash-emandate.my-enrollments') }}" class="btn btn-outline-secondary me-md-2">
                                <i class="fas fa-list me-2"></i>My E-Mandates
                            </a>
                        @endauth
                        <a href="{{ url('/') }}" class="btn btn-primary">
                            <i class="fas fa-home me-2"></i>Back to Home
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
