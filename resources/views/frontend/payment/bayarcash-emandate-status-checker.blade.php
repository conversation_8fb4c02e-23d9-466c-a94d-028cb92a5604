@extends('layouts.auth')

@section('title', 'E-Mandate Status Checker')

@section('content')
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-search me-2"></i>
                        E-Mandate Status Checker
                    </h4>
                    <p class="mb-0 mt-2">Check the status of your FPX Direct Debit e-mandate enrollment</p>
                </div>
                
                <div class="card-body">
                    <!-- Status Check Form -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <form id="statusCheckForm">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="order_number" class="form-label">Order Number <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="order_number" name="order_number" 
                                                   placeholder="Enter your order number (e.g., EMANDATE_xxxxx)" required>
                                            <div class="form-text">You can find this in your enrollment confirmation email</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">&nbsp;</label>
                                            <div class="d-grid">
                                                <button type="submit" class="btn btn-primary" id="checkStatusBtn">
                                                    <i class="fas fa-search me-2"></i>Check Status
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Loading Indicator -->
                    <div id="loadingIndicator" class="text-center d-none">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Checking status...</p>
                    </div>

                    <!-- Error Message -->
                    <div id="errorMessage" class="alert alert-danger d-none">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="errorText"></span>
                    </div>

                    <!-- Status Result -->
                    <div id="statusResult" class="d-none">
                        <div class="row mb-3">
                            <div class="col-12">
                                <h5 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-info-circle me-2"></i>Enrollment Status
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Order Number:</strong></td>
                                        <td><code id="resultOrderNumber"></code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Status:</strong></td>
                                        <td><span id="resultStatus" class="badge"></span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Amount:</strong></td>
                                        <td>RM <span id="resultAmount"></span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Frequency:</strong></td>
                                        <td id="resultFrequency"></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Effective Date:</strong></td>
                                        <td id="resultEffectiveDate"></td>
                                    </tr>
                                    <tr id="enrolledAtRow" class="d-none">
                                        <td><strong>Enrolled At:</strong></td>
                                        <td id="resultEnrolledAt"></td>
                                    </tr>
                                    <tr id="nextPaymentRow" class="d-none">
                                        <td><strong>Next Payment:</strong></td>
                                        <td id="resultNextPayment"></td>
                                    </tr>
                                    <tr id="statusMessageRow" class="d-none">
                                        <td><strong>Message:</strong></td>
                                        <td id="resultStatusMessage"></td>
                                    </tr>
                                </table>
                            </div>
                        </div>

                        <!-- Status-specific information -->
                        <div id="statusInfo" class="alert">
                            <div id="statusInfoContent"></div>
                        </div>
                    </div>

                    <!-- Help Section -->
                    <div class="row mt-5">
                        <div class="col-12">
                            <h6 class="text-secondary border-bottom pb-2">
                                <i class="fas fa-question-circle me-2"></i>Need Help?
                            </h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card border-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-envelope me-2 text-primary"></i>Contact Support
                                            </h6>
                                            <p class="card-text small">
                                                If you're having trouble finding your order number or need assistance with your e-mandate, 
                                                please contact our support team.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card border-light">
                                        <div class="card-body">
                                            <h6 class="card-title">
                                                <i class="fas fa-redo me-2 text-success"></i>New Enrollment
                                            </h6>
                                            <p class="card-text small">
                                                Want to set up a new e-mandate? 
                                                <a href="{{ route('payment.bayarcash-emandate.enrollment') }}" class="text-decoration-none">
                                                    Click here to start a new enrollment
                                                </a>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('statusCheckForm');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const errorMessage = document.getElementById('errorMessage');
    const statusResult = document.getElementById('statusResult');
    const checkStatusBtn = document.getElementById('checkStatusBtn');

    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const orderNumber = document.getElementById('order_number').value.trim();
        if (!orderNumber) {
            showError('Please enter an order number');
            return;
        }

        checkStatus(orderNumber);
    });

    function checkStatus(orderNumber) {
        // Show loading, hide other elements
        loadingIndicator.classList.remove('d-none');
        errorMessage.classList.add('d-none');
        statusResult.classList.add('d-none');
        checkStatusBtn.disabled = true;

        // Make API request
        fetch('{{ route("payment.bayarcash-emandate.check-status") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ order_number: orderNumber })
        })
        .then(response => response.json())
        .then(data => {
            loadingIndicator.classList.add('d-none');
            checkStatusBtn.disabled = false;

            if (data.success) {
                showStatusResult(data.data);
            } else {
                showError(data.message || 'Failed to check status');
            }
        })
        .catch(error => {
            loadingIndicator.classList.add('d-none');
            checkStatusBtn.disabled = false;
            showError('An error occurred while checking status');
            console.error('Error:', error);
        });
    }

    function showStatusResult(data) {
        // Populate result fields
        document.getElementById('resultOrderNumber').textContent = data.order_number;
        document.getElementById('resultAmount').textContent = parseFloat(data.amount).toFixed(2);
        document.getElementById('resultFrequency').textContent = data.frequency_mode.toLowerCase();
        document.getElementById('resultEffectiveDate').textContent = formatDate(data.effective_date);

        // Set status badge
        const statusBadge = document.getElementById('resultStatus');
        statusBadge.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
        statusBadge.className = 'badge ' + getStatusBadgeClass(data.status);

        // Show optional fields
        if (data.enrolled_at) {
            document.getElementById('resultEnrolledAt').textContent = formatDateTime(data.enrolled_at);
            document.getElementById('enrolledAtRow').classList.remove('d-none');
        }

        if (data.next_payment_at) {
            document.getElementById('resultNextPayment').textContent = formatDate(data.next_payment_at);
            document.getElementById('nextPaymentRow').classList.remove('d-none');
        }

        if (data.status_message) {
            document.getElementById('resultStatusMessage').textContent = data.status_message;
            document.getElementById('statusMessageRow').classList.remove('d-none');
        }

        // Show status-specific information
        showStatusInfo(data);

        statusResult.classList.remove('d-none');
    }

    function showStatusInfo(data) {
        const statusInfo = document.getElementById('statusInfo');
        const statusInfoContent = document.getElementById('statusInfoContent');
        
        let content = '';
        let alertClass = 'alert-info';

        switch (data.status) {
            case 'active':
                alertClass = 'alert-success';
                content = `
                    <h6><i class="fas fa-check-circle me-2"></i>E-Mandate Active</h6>
                    <p class="mb-0">Your e-mandate is active and ready for recurring payments. 
                    The next payment will be processed on ${formatDate(data.next_payment_at)}.</p>
                `;
                break;
            case 'pending':
                alertClass = 'alert-warning';
                content = `
                    <h6><i class="fas fa-clock me-2"></i>Enrollment Pending</h6>
                    <p class="mb-0">Your e-mandate enrollment is still being processed. 
                    Please check back later or contact support if this status persists.</p>
                `;
                break;
            case 'failed':
                alertClass = 'alert-danger';
                content = `
                    <h6><i class="fas fa-times-circle me-2"></i>Enrollment Failed</h6>
                    <p class="mb-0">Your e-mandate enrollment was unsuccessful. 
                    You may try enrolling again or contact support for assistance.</p>
                `;
                break;
            case 'cancelled':
                alertClass = 'alert-secondary';
                content = `
                    <h6><i class="fas fa-ban me-2"></i>E-Mandate Cancelled</h6>
                    <p class="mb-0">This e-mandate has been cancelled and is no longer active.</p>
                `;
                break;
            case 'expired':
                alertClass = 'alert-warning';
                content = `
                    <h6><i class="fas fa-calendar-times me-2"></i>E-Mandate Expired</h6>
                    <p class="mb-0">This e-mandate has expired and is no longer active.</p>
                `;
                break;
            default:
                content = `
                    <h6><i class="fas fa-info-circle me-2"></i>Status: ${data.status}</h6>
                    <p class="mb-0">Current status of your e-mandate enrollment.</p>
                `;
        }

        statusInfo.className = 'alert ' + alertClass;
        statusInfoContent.innerHTML = content;
    }

    function showError(message) {
        document.getElementById('errorText').textContent = message;
        errorMessage.classList.remove('d-none');
    }

    function getStatusBadgeClass(status) {
        switch (status) {
            case 'active': return 'bg-success';
            case 'pending': return 'bg-warning';
            case 'failed': return 'bg-danger';
            case 'cancelled': return 'bg-secondary';
            case 'expired': return 'bg-warning';
            default: return 'bg-info';
        }
    }

    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', { 
            day: '2-digit', 
            month: 'short', 
            year: 'numeric' 
        });
    }

    function formatDateTime(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleDateString('en-GB', { 
            day: '2-digit', 
            month: 'short', 
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
});
</script>
@endsection
